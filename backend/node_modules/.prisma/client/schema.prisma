generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = "file:../../database/sfm_dashboard.db"
  // Der relative Pfad ist vom backend/prisma-Verzeichnis aus gesehen
}

model Ablaengerei {
  id           Int     @id @default(autoincrement())
  Datum        String?
  cutLagerK220 Int?
  cutLagerR220 Int?
  lagerCut220  Int?
  cutLagerK240 Int?
  cutLagerR240 Int?
  lagerCut240  Int?
  cutTT        Int?
  cutTR        Int?
  cutRR        Int?
  cutGesamt    Int?
  pickCut      Int?
  cutLager200  Int?
  cutLagerK200 Int?
  lagerCut200  Int?
}

model Schnitte {
  id        Int     @id @default(autoincrement())
  Datum     String?
  M5_R_H1   Int?    @map("M5-R-H1")
  M6_T_H1   Int?    @map("M6-T-H1")
  M7_R_H1   Int?    @map("M7-R-H1")
  M8_T_H1   Int?    @map("M8-T-H1")
  M9_R_H1   Int?    @map("M9-R-H1")
  M10_T_H1  Int?    @map("M10-T-H1")
  M11_R_H1  Int?    @map("M11-R-H1")
  M12_T_H1  Int?    @map("M12-T-H1")
  M13_R_H1  Int?    @map("M13-R-H1")
  M14_T_H1  Int?    @map("M14-T-H1")
  M15_R_H1  Int?    @map("M15-R-H1")
  M16_T_H1  Int?    @map("M16-T-H1")
  M17_R_H1  Int?    @map("M17-R-H1")
  M18_T_H1  Int?    @map("M18-T-H1")
  M19_T_H1  Int?    @map("M19-T-H1")
  M20_T_H1  Int?    @map("M20-T-H1")
  M21_R_H1  Int?    @map("M21-R-H1")
  M23_T_H1  Int?    @map("M23-T-H1")
  M25_RR_H1 Int?    @map("M25-RR-H1")
  M26_T_H1  Int?    @map("M26-T-H1")
  Sum_H1    Int?    @map("Sum-H1")
  M1_T_H3   Int?    @map("M1-T-H3")
  M2_T_H3   Int?    @map("M2-T-H3")
  M3_R_H3   Int?    @map("M3-R-H3")
  M4_T_H3   Int?    @map("M4-T-H3")
  M22_T_H3  Int?    @map("M22-T-H3")
  M24_T_H3  Int?    @map("M24-T-H3")
  M27_R_H3  Int?    @map("M27-R-H3")
  Sum_H3    Int?    @map("Sum-H3")

  @@map("schnitte")
}

model WE {
  id     Int     @id @default(autoincrement())
  Datum  String?
  weAtrl Int?
  weManl Int?
}

model Bestand200 {
  id                   Int       @id @default(autoincrement())
  Lagertyp             String?
  Lagerplatz           String?
  Material             String?
  Charge               String?
  Dauer                Float?
  Lagerbereich         String?
  Lagerplatztyp        String?
  Lagerplatzaufteilung String?
  Auslagerungssperre   String?
  Einlagerungssperre   String?
  Sperrgrund           String?
  Letzte_Bewegung      String?   @map("Letzte Bewegung")
  Uhrzeit              String?
  TA_Nummer            String?   @map("TA-Nummer")
  TA_Position          String?   @map("TA-Position")
  Letzter__nderer      String?   @map("Letzter Änderer")
  Letzte__nderung      String?   @map("Letzte Änderung")
  Wareneingangsdatum   String?
  WE_Nummer            String?   @map("WE-Nummer")
  WE_Position          String?   @map("WE-Position")
  Lieferung            String?
  Position             String?
  Lagereinheitentyp    String?
  Gesamtbestand        Float?
  Lagereinheit         String?
  aufnahmeDatum        String?
  aufnahmeZeit         String?
  maxPlaetze           String?
  auslastung           String?
  maxA                 String?
  maxB                 String?
  maxC                 String?
  auslastungA          String?
  auslastungB          String?
  auslastungC          String?
  import_timestamp     DateTime? @default(now())

  @@map("bestand200")
}

model DispatchData {
  id                   Int    @id @default(autoincrement())
  datum                String
  tag                  Int
  monat                Int
  kw                   Int
  jahr                 Int?
  servicegrad          Float
  ausgeliefert_lup     Int
  rueckstaendig        Int
  produzierte_tonnagen Float
  direktverladung_kiaa Int
  umschlag             Int
  kg_pro_colli         Float
  elefanten            Int
  atrl                 Int
  aril                 Int
  fuellgrad_aril       Float
  qm_angenommen        Int
  qm_abgelehnt         Int
  qm_offen             Int
  mitarbeiter_std      Float

  @@map("dispatch_data")
}

model ARiL {
  id                        Int       @id @default(autoincrement())
  Datum                     DateTime?
  waTaPositionen            Int?
  Umlagerungen              Int?
  belegtePlaetze            Int?
  systemtablareRuecksackStk Int?
  systemtablareGesamtStk    Int?
  systemtablareEinzelBelegt Int?
  belegtRinge               Int?
  Auslastung                Float?
  alleBewegungen            Int?
  cuttingLagerKunde         Int?
  cuttingLagerRest          Int?
  lagerCutting              Int?
}

model ATrL {
  id                                  Int       @id @default(autoincrement())
  Datum                               DateTime?
  umlagerungen                        Int?
  waTaPositionen                      Int?
  belegtePlaetze                      Int?
  davonSystempaletten                 Int?
  SystempalettenstapelRucksackpaetzen Int?
  SystempalettenstapelEinzel          Int?
  PlaetzeSystempalettenstapelEinzel   Int?
  plaetzeMitTrommelBelegt             Int?
  Auslastung                          Float?
  Bewegungen                          Int?
  EinlagerungAblKunde                 Int?
  EinlagerungAblRest                  Int?
  AuslagerungAbl                      Int?
  weAtrl                              Int?
}

model ManL {
  id                    Int       @id @default(autoincrement())
  Datum                 DateTime?
  Wareneingang          Int?
  cuttingLagerKunde     Int?
  cuttingLagerRest      Int?
  ruecklagerungVonKommi Int?
  waTaPos               Int?
  lagerCutting          Int?
  belegtePlaetze        Int?
  Auslastung            Float?
  alleBewegungen        Int?
}

model System {
  id                                 Int       @id @default(autoincrement())
  Datum                              DateTime?
  nioSapAtrl                         Int?
  nioWitronAtrl                      Int?
  nioSiemensAtrl                     Int?
  nioProzessAtrl                     Int?
  nioSonstigesAtrl                   Int?
  nioSapAril                         Int?
  nioWitronAril                      Int?
  nioSiemensAril                     Int?
  nioProzessAril                     Int?
  nioSonstigesAril                   Int?
  verfuegbarkeitAnzahlStoerungenAtrl Float?
  verfuegbarkeitAnzahlStoerungenAril Float?
  verfuegbarkeitAtrl_FT_RBG_MFR1     Float?
  verfuegbarkeitAril_FT_RBG          Float?
  verfuegbarkeitFTS                  Float?
  gesamtverfuegbarkeit_AtrL_ARiL_FTS Float?
  verfuegbarkeitITM                  Float?
  verfuegbarkeitSAP                  Float?
  verfuegbarkeitServicegrad          Float?
  weGesamtAtrl_Manl                  Int?
  waTaPosGesamt_Atrl_Manl_Aril_Abl   Int?
}

model alle_daten {
  id                         Int      @id @default(autoincrement())
  datum                      DateTime
  we_atrl                    Int?
  cutk_lager_atrl            Int?
  cutr_lager_atrl            Int?
  lo_lo_atrl                 Int?
  uml_atrl                   Int?
  wa_pos_atrl                Int?
  lager_cut_atrl             Int?
  plaetze_bel_atrl           Int?
  sysp_atrl                  Int?
  sysp_rucks_atrl            Int?
  sysst_einz_atrl            Int?
  sysp_einz_bel_atrl         Int?
  trom_bel_atrl              Int?
  fuellgrad_atrl             Float?
  all_bew_atrl               Int?
  we_gesamt_atrl             Int?
  we_manl                    Int?
  cut_lager_kunde_manl       Int?
  cut_lager_rest_995_manl    Int?
  lagerol_lagero_312_manl    Int?
  ausl_kommi_manl            Int?
  rueck_kommi_manl           Int?
  wa_pos_601_manl            Int?
  lager_cut_994_manl         Int?
  plaetze_bel_manl           Int?
  fuellgrad_manl             Float?
  all_bew_manl               Int?
  we_manl_2                  Int?
  cut_lager_kunde_996_aril   Int?
  cut_lager_rest_aril        Int?
  wa_pos_601_aril            Int?
  uml_aril                   Int?
  lager_cut_aril             Int?
  plaetze_bel_aril           Int?
  systab_stk_aril            Int?
  systab_rucks_stk_aril      Int?
  systab_einzel_stk_aril     Int?
  systab_belegt_einzel_aril  Int?
  ring_belegt_aril           Int?
  fuellgrad_aril             Float?
  bew_aril                   Int?
  ta_sesamt_abl              Int?
  ta_tt_abl                  Int?
  ta_tr_abl                  Int?
  ta_rr_abl                  Int?
  schnitte_ges_abl           Int?
  cuttopick_abl              Int?
  atrl_sap_ni                Int?
  atrl_witron_nio            Int?
  atrl_siemens_nio           Int?
  atrl_prozess_nio           Int?
  atrl_sonst_nio             Int?
  aril_sap_nio               Int?
  aril_witron_nio            Int?
  aril_siemens_nio           Int?
  aril_nio                   Int?
  aril2_nio                  Int?
  atrl_st_rung_verf          Int?     @map("atrl_störung_verf")
  aril_st_rung_verf          Int?     @map("aril_störung_verf")
  fts_rbg_mfr_st_rung_verf   Int?     @map("fts_rbg_mfr_störung_verf")
  fts_rbg_st_rung_verf       Int?     @map("fts_rbg_störung_verf")
  fts_st_rung_verf           Int?     @map("fts_störung_verf")
  aril_atrl_fts_st_rung_verf Int?     @map("aril_atrl_fts_störung_verf")
  itm_st_rung_verf           Int?     @map("itm_störung_verf")
  sap_st_rung_verf           Int?     @map("sap_störung_verf")
  servicegrad                Float?
  wa_einzel_pos              Int?
  we                         Int?
  wa                         Int?
  wa_lagebest                Int?
  wa_mehr_pos                Int?
  we_ges_manl_atrl           Int?
  wa_ges_abl_manl_atrl_aril  Int?
}

model auslastung200 {
  id               Int       @id @default(autoincrement())
  aufnahmeDatum    String?
  aufnahmeZeit     String?
  maxPlaetze       String?
  auslastung       String?
  maxA             String?
  maxB             String?
  maxC             String?
  auslastungA      String?
  auslastungB      String?
  auslastungC      String?
  import_timestamp DateTime? @default(now())
}

model auslastung240 {
  id               Int       @id @default(autoincrement())
  aufnahmeDatum    String?
  aufnahmeZeit     String?
  maxPlaetze       String?
  auslastung       String?
  maxA             String?
  maxB             String?
  maxC             String?
  auslastungA      String?
  auslastungB      String?
  auslastungC      String?
  import_timestamp DateTime? @default(now())
}

model bestand240 {
  id                   Int       @id @default(autoincrement())
  Lagertyp             String?
  Lagerplatz           String?
  Material             String?
  Charge               String?
  Dauer                Float?
  Lagerbereich         String?
  Lagerplatztyp        String?
  Lagerplatzaufteilung String?
  Auslagerungssperre   String?
  Einlagerungssperre   String?
  Sperrgrund           String?
  Letzte_Bewegung      String?   @map("Letzte Bewegung")
  Uhrzeit              String?
  TA_Nummer            String?   @map("TA-Nummer")
  TA_Position          String?   @map("TA-Position")
  Letzter__nderer      String?   @map("Letzter Änderer")
  Letzte__nderung      String?   @map("Letzte Änderung")
  Wareneingangsdatum   String?
  WE_Nummer            String?   @map("WE-Nummer")
  WE_Position          String?   @map("WE-Position")
  Lieferung            String?
  Position             String?
  Lagereinheitentyp    String?
  Gesamtbestand        Float?
  Lagereinheit         String?
  aufnahmeDatum        String?
  aufnahmeZeit         String?
  maxPlaetze           String?
  auslastung           String?
  maxA                 String?
  maxB                 String?
  maxC                 String?
  auslastungA          String?
  auslastungB          String?
  auslastungC          String?
  import_timestamp     DateTime? @default(now())
}

model bestandRest {
  id                   Int       @id @default(autoincrement())
  Lagertyp             String?
  Lagerplatz           String?
  Material             String?
  Charge               String?
  Dauer                Float?
  Lagerbereich         String?
  Lagerplatztyp        String?
  Lagerplatzaufteilung String?
  Auslagerungssperre   String?
  Einlagerungssperre   String?
  Sperrgrund           String?
  Letzte_Bewegung      String?   @map("Letzte Bewegung")
  Uhrzeit              String?
  TA_Nummer            String?   @map("TA-Nummer")
  TA_Position          String?   @map("TA-Position")
  Letzter__nderer      String?   @map("Letzter Änderer")
  Letzte__nderung      String?   @map("Letzte Änderung")
  Wareneingangsdatum   String?
  WE_Nummer            String?   @map("WE-Nummer")
  WE_Position          String?   @map("WE-Position")
  Lieferung            String?
  Position             String?
  Lagereinheitentyp    String?
  Gesamtbestand        Float?
  Lagereinheit         String?
  aufnahmeDatum        String?
  aufnahmeZeit         String?
  import_timestamp     DateTime? @default(now())
}

model maschinen {
  id             Int     @id @default(autoincrement())
  Machine        String? @unique(map: "sqlite_autoindex_maschinen_1")
  schnitteProStd Float?
}

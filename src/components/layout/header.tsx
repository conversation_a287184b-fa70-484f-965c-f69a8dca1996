/**
 * Header Component - Kopfzeile der Anwendung
 */

import React from 'react';
import { useLocation } from '@tanstack/react-router';

export function Header() {
  const location = useLocation();
  
  // Bestimme den Seitentitel basierend auf der aktuellen Route
  const getPageTitle = () => {
    switch (location.pathname) {
      case '/':
        return 'Dashboard';
      case '/cutting':
        return 'Cutting Management';
      case '/warehouse':
        return 'Warehouse Management';
      case '/system':
        return 'System Management';
      case '/chat':
        return 'AI Chat Assistant';
      case '/database-manager':
        return 'Database Manager';
      case '/settings':
        return 'Settings';
      default:
        return 'SFM Dashboard';
    }
  };

  return (
    <header className="bg-card border-b border-border px-6 py-4">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-foreground">
          {getPageTitle()}
        </h2>
        
        <div className="flex items-center space-x-4">
          {/* <PERSON>er können weitere Header-Elemente hinzugefügt werden */}
          <div className="text-sm text-muted-foreground">
            {new Date().toLocaleDateString('de-DE')}
          </div>
        </div>
      </div>
    </header>
  );
}

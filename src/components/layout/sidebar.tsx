/**
 * Sidebar Component - Navigation der Anwendung
 */

import React from 'react';
import { Link, useLocation } from '@tanstack/react-router';
import { 
  LayoutDashboard, 
  Scissors, 
  Warehouse, 
  Settings, 
  Database,
  MessageSquare,
  Activity
} from 'lucide-react';

const navigation = [
  { name: 'Dashboard', href: '/', icon: LayoutDashboard },
  { name: 'Cutting', href: '/cutting', icon: Scissors },
  { name: 'Warehouse', href: '/warehouse', icon: Warehouse },
  { name: 'System', href: '/system', icon: Activity },
  { name: 'Chat', href: '/chat', icon: MessageSquare },
  { name: 'Database', href: '/database-manager', icon: Database },
  { name: 'Settings', href: '/settings', icon: Settings },
];

export function Sidebar() {
  const location = useLocation();

  return (
    <div className="w-64 bg-card border-r border-border flex flex-col">
      {/* Logo/Title */}
      <div className="p-6 border-b border-border">
        <h1 className="text-xl font-bold text-foreground">
          SFM Dashboard
        </h1>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link
              key={item.name}
              to={item.href}
              className={`
                flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors
                ${isActive 
                  ? 'bg-primary text-primary-foreground' 
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent'
                }
              `}
            >
              <Icon className="mr-3 h-5 w-5" />
              {item.name}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}

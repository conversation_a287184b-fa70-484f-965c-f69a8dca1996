/**
 * Root Route Component - Hauptlayout der Anwendung
 * 
 * Diese Komponente stellt das Grundlayout für alle Seiten bereit.
 */

import React from 'react';
import { Outlet } from '@tanstack/react-router';
import { Sidebar } from '@/components/layout/sidebar';
import { Header } from '@/components/layout/header';

export function RootRouteComponent() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Hauptlayout mit Sidebar und Content */}
      <div className="flex h-screen">
        {/* Sidebar */}
        <Sidebar />
        
        {/* Hauptinhalt */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <Header />
          
          {/* Seiteninhalt */}
          <main className="flex-1 overflow-auto p-6">
            <Outlet />
          </main>
        </div>
      </div>
    </div>
  );
}

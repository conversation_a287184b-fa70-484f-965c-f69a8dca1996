import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { RouterProvider } from "@tanstack/react-router";
import { syncThemeWithLocal } from "@/helpers/theme_helpers";
import { updateAppLanguage } from "@/helpers/language_helpers";
import { router } from "@/routes/router";
import ChatBot from "@/components/chat/ChatBot";
import Toaster from "@/components/ui/toaster";
import "./localization/i18n";

// Importiere die globale API für Rückwärtskompatibilität
import "./services/global-api";

/**
 * Hauptkomponente der Anwendung
 * 
 * Verantwortlich für:
 * - Initialisierung von Theme und Sprache
 * - Bereitstellung des Routers
 */
function App() {
  const { i18n } = useTranslation();
  const [isBackendReady, setIsBackendReady] = useState(false);

  useEffect(() => {
    console.log("🚀 App-Initialisierung gestartet");
    
    // Initialisiere Theme und Sprache beim Start
    const savedTheme = localStorage.getItem('theme') || 'system';
    syncThemeWithLocal(savedTheme);
    updateAppLanguage(i18n);
    
    // Prüfe die Backend-Verbindung
    checkBackendConnection();
    
    console.log("✅ App vollständig initialisiert und funktional!");
  }, [i18n]);

  const checkBackendConnection = async () => {
    try {
      const response = await fetch('http://localhost:3001/api/health');
      if (response.ok) {
        setIsBackendReady(true);
      }
    } catch (error) {
      console.warn('Backend nicht erreichbar, Chat-Funktionalität deaktiviert', error);
    }
  };

  return (
    <React.StrictMode>
      <RouterProvider router={router} />
      {isBackendReady && <ChatBot />}
      <Toaster />
    </React.StrictMode>
  );
}

export default App;

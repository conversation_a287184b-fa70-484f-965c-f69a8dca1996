import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Link } from "@tanstack/react-router";
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Truck, Scissors, PackageCheck, Clock, Home } from "lucide-react";
import { Progress } from "@/components/ui/progress";

// Interface für echte KPI-Daten aus der Datenbank
interface DepartmentKPIs {
  dispatch: {
    serviceLevel: number;
    targetServiceLevel: number;
    pickingPerformance: number; // Summe von ATRL + ARIL (keine Prozent)
    deliveryPerformance: number;
    qualityRate: number;
    producedTonnage: number; // Aus TagesleistungData
    directLoading: number; // Aus TagesleistungData
    umschlag: number; // Aus TagesleistungData
    kgPerColli: number; // Aus TagesleistungData
    elefanten: number; // Aus TagesleistungData
    lastUpdated: string;
  };
  ablaengerei: {
    schnitte: number; // Summe von pickCut + cutRR + cutTR + cutTT
    schnitteTT: number; // cutTT
    schnitteTR: number; // cutTR
    schnitteRR: number; // cutRR
    schnitteCut2Pick: number; // pickCut
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
  wareneingang: {
    weAtrlPos: number; // weAtrl - Automatische WE-Positionen
    weManuellePos: number; // weManl - Manuelle WE-Positionen
    gesamtWE: number; // Summe von weAtrl + weManl
    automatisierungsgrad: number; // Prozentsatz automatischer WE
    qualityRate: number; // Gleiche Berechnung wie bei Dispatch
    lastUpdated: string;
  };
}

// Interface für Datenbankdaten
interface ServiceLevelData {
  datum: string;
  servicegrad: number;
}

interface PickingData {
  date: string;
  atrl: number;
  aril: number;
  fuellgrad_aril: number;
}

interface DeliveryData {
  date: string;
  ausgeliefert_lup: number;
  rueckstaendig: number;
}

interface QMData {
  name: string;
  value: number;
  fill: string;
  date?: string;
}

interface TagesleistungData {
  date: string;
  produzierte_tonnagen: number;
  direktverladung_kiaa: number;
  umschlag: number;
  kg_pro_colli: number;
  elefanten: number;
}

interface AblaengereiData {
  id?: number;
  datum: string;
  cutTT: number | null;
  cutTR: number | null;
  cutRR: number | null;
  pickCut: number | null;
}

interface WEData {
  id?: number;
  datum: string;
  weAtrl: number | null;
  weManl: number | null;
}

/**
 * Startseite des Shopfloor-Management-Dashboards
 * 
 * Zeigt eine Übersicht über alle verfügbaren Abteilungen mit echten KPI-Daten
 * aus der Datenbank für den gestrigen Tag.
 * Das Design folgt dem Neobrutalism-Stil mit kräftigen Farben und starken Konturen.
 */
export default function HomePage() {
  const { t } = useTranslation();
  const [departmentKPIs, setDepartmentKPIs] = useState<DepartmentKPIs | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Funktion zur Rückgabe des festen Datums für die KPI-Anzeige
  const getTargetDate = (): string => {
    return '2025-01-10'; // Festes Datum, da aktuelle Daten noch nicht verfügbar
  };

  // Funktion zum Laden aller KPI-Daten für den gestrigen Tag
  const loadKPIData = async (): Promise<void> => {
    try {
      setLoading(true);
      setError(null);
      
      const targetDate = getTargetDate();
      console.log('Lade KPI-Daten für:', targetDate);

      // Parallel alle Datenquellen über HTTP-APIs laden
      const [
        serviceLevelResult,
        pickingResult,
        deliveryResult,
        qmResult,
        tagesleistungResult,
        ablaengereiResult,
        weResult
      ] = await Promise.all([
        fetch('http://localhost:3001/api/warehouse/service-level').then(r => r.json()).then(d => d.data).catch(() => []),
        fetch('http://localhost:3001/api/warehouse/picking').then(r => r.json()).then(d => d.data).catch(() => []),
        fetch('http://localhost:3001/api/warehouse/delivery').then(r => r.json()).then(d => d.data).catch(() => []),
        fetch('http://localhost:3001/api/system/qm').then(r => r.json()).then(d => d.data).catch(() => []),
        fetch('http://localhost:3001/api/system/tagesleistung').then(r => r.json()).then(d => d.data).catch(() => []),
        fetch('http://localhost:3001/api/cutting/ablaengerei').then(r => r.json()).then(d => d.data).catch(() => []),
        fetch('http://localhost:3001/api/warehouse/wareneingang').then(r => r.json()).then(d => d.data).catch(() => [])
      ]);

      // Filtere Daten für das Zieldatum und berechne KPIs
      const kpis = calculateKPIs(
        targetDate,
        serviceLevelResult,
        pickingResult,
        deliveryResult,
        qmResult,
        tagesleistungResult,
        ablaengereiResult,
        weResult
      );

      setDepartmentKPIs(kpis);
    } catch (err) {
      console.error('Fehler beim Laden der KPI-Daten:', err);
      setError('Fehler beim Laden der Dashboard-Daten');
    } finally {
      setLoading(false);
    }
  };

  // Funktion zur Berechnung der KPIs aus den Rohdaten
  const calculateKPIs = (
    targetDate: string,
    serviceLevelData: unknown[],
    pickingData: unknown[],
    deliveryData: unknown[],
    qmData: unknown[],
    tagesleistungData: unknown[],
    ablaengereiData: unknown[],
    weData: unknown[]
  ): DepartmentKPIs => {
    const lastUpdated = new Date().toLocaleTimeString('de-DE');

    // Servicegrad für gestrigen Tag
    const serviceLevelItem = serviceLevelData?.find(item => (item as ServiceLevelData).datum === targetDate) as ServiceLevelData | undefined;
    let serviceLevel = serviceLevelItem?.servicegrad || 0;
    // Konvertiere Dezimalwerte zu Prozent
    if (serviceLevel > 0 && serviceLevel <= 1) {
      serviceLevel = serviceLevel * 100;
    }

    // Picking-Performance für gestrigen Tag (Summe von ATRL und ARIL, keine Prozent)
    const pickingItem = pickingData?.find(item => (item as PickingData).date === targetDate) as PickingData | undefined;
    const pickingPerformance = pickingItem 
      ? pickingItem.atrl + pickingItem.aril 
      : 0;

    // Delivery-Performance für gestrigen Tag (Verhältnis ausgeliefert zu gesamt)
    const deliveryItem = deliveryData?.find(item => (item as DeliveryData).date === targetDate) as DeliveryData | undefined;
    const deliveryPerformance = deliveryItem && (deliveryItem.ausgeliefert_lup + deliveryItem.rueckstaendig) > 0
      ? (deliveryItem.ausgeliefert_lup / (deliveryItem.ausgeliefert_lup + deliveryItem.rueckstaendig)) * 100
      : 0;

    // Qualitätsrate für gestrigen Tag (Anteil angenommener QM-Meldungen)
    const qmYesterdayData = qmData?.filter(item => 
      !(item as QMData).date || (item as QMData).date === targetDate
    ) as QMData[];
    const totalQM = qmYesterdayData?.reduce((sum, item) => sum + item.value, 0) || 0;
    const acceptedQM = qmYesterdayData?.find(item => item.name.toLowerCase().includes('angenommen'))?.value || 0;
    const qualityRate = totalQM > 0 ? (acceptedQM / totalQM) * 100 : 0;

    // Tagesleistung für gestrigen Tag (gehört zum Versand)
    const tagesleistungItem = tagesleistungData?.find(item => (item as TagesleistungData).date === targetDate) as TagesleistungData | undefined;
    const producedTonnage = tagesleistungItem?.produzierte_tonnagen || 0;
    const kgPerColli = tagesleistungItem?.kg_pro_colli || 0;
    const umschlag = tagesleistungItem?.umschlag || 0;
    const directLoading = tagesleistungItem?.direktverladung_kiaa || 0;
    const elefanten = tagesleistungItem?.elefanten || 0;

    // Ablängerei-Daten für das Zieldatum
    console.log('DEBUG: Alle Ablängerei-Daten:', ablaengereiData);
    console.log('DEBUG: Suche nach Datum:', targetDate);
    
    // Versuche verschiedene Datumsformate zu finden
    let ablaengereiItem = ablaengereiData?.find(item => (item as AblaengereiData).datum === targetDate) as AblaengereiData | undefined;
    
    // Falls nicht gefunden, versuche andere Datumsformate
    if (!ablaengereiItem) {
      // Versuche ohne Bindestriche: '20250110'
      const dateWithoutDashes = targetDate.replace(/-/g, '');
      ablaengereiItem = ablaengereiData?.find(item => 
        (item as AblaengereiData).datum === dateWithoutDashes
      ) as AblaengereiData | undefined;
    }
    
    if (!ablaengereiItem) {
      // Versuche deutsches Format: '10.01.2025'
      const germanDate = targetDate.split('-').reverse().join('.');
      ablaengereiItem = ablaengereiData?.find(item => 
        (item as AblaengereiData).datum === germanDate
      ) as AblaengereiData | undefined;
    }
    
    if (!ablaengereiItem) {
      // Falls immer noch nicht gefunden, zeige verfügbare Daten
      console.log('DEBUG: Keine Daten für', targetDate, 'gefunden! Verfügbare Daten (erste 10):');
      const availableDates = ablaengereiData?.slice(0, 10).map(item => (item as AblaengereiData).datum);
      console.log('DEBUG: Verfügbare Datumsformate:', availableDates);
      
      // Verwende den neuesten verfügbaren Datensatz als Fallback
      if (ablaengereiData && ablaengereiData.length > 0) {
        ablaengereiItem = ablaengereiData[0] as AblaengereiData;
        console.log('DEBUG: Verwende Fallback-Datensatz:', ablaengereiItem);
      }
    }
    
    console.log('DEBUG: Gefundener Ablängerei-Eintrag:', ablaengereiItem);
    
    const cutTT = ablaengereiItem?.cutTT || 0;
    const cutTR = ablaengereiItem?.cutTR || 0;
    const cutRR = ablaengereiItem?.cutRR || 0;
    const pickCut = ablaengereiItem?.pickCut || 0;
    const schnitte = cutTT + cutTR + cutRR + pickCut; // Summe aller Schnitte
    
    console.log('DEBUG: Berechnete Schnitte:', { cutTT, cutTR, cutRR, pickCut, schnitte });

    // Qualitätsrate für Ablängerei (gleiche Berechnung wie bei Dispatch)
    const ablaengereiQualityRate = qualityRate; // Verwende die gleiche QM-Qualitätsrate

    // Wareneingang-Daten für das Zieldatum
    const weItem = weData?.find(item => (item as WEData).datum === targetDate) as WEData | undefined;
    const weAtrl = weItem?.weAtrl || 0;
    const weManl = weItem?.weManl || 0;
    const gesamtWE = weAtrl + weManl;
    const automatisierungsgrad = gesamtWE > 0 ? (weAtrl / gesamtWE) * 100 : 0;

    // Qualitätsrate für Wareneingang (gleiche Berechnung wie bei Dispatch)
    const wareneingangQualityRate = qualityRate; // Verwende die gleiche QM-Qualitätsrate

    return {
      dispatch: {
        serviceLevel,
        targetServiceLevel: 98.5,
        pickingPerformance,
        deliveryPerformance,
        qualityRate,
        producedTonnage,
        directLoading,
        umschlag,
        kgPerColli,
        elefanten,
        lastUpdated
      },
      ablaengerei: {
        schnitte,
        schnitteTT: cutTT,
        schnitteTR: cutTR,
        schnitteRR: cutRR,
        schnitteCut2Pick: pickCut,
        qualityRate: ablaengereiQualityRate,
        lastUpdated
      },
      wareneingang: {
        weAtrlPos: weAtrl,
        weManuellePos: weManl,
        gesamtWE,
        automatisierungsgrad,
        qualityRate: wareneingangQualityRate,
        lastUpdated
      }
    };
  };

  // Lade KPI-Daten beim Component Mount
  useEffect(() => {
    loadKPIData();
  }, []);

  // Loading State
  if (loading) {
    return (
      <div className="w-full bg-bg min-h-screen p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-black mb-4"></div>
          <p className="font-heading text-xl">Dashboard wird geladen...</p>
        </div>
      </div>
    );
  }

  // Error State
  if (error || !departmentKPIs) {
    return (
      <div className="w-full bg-bg min-h-screen p-8 flex items-center justify-center">
        <div className="flex flex-col items-center text-red-500">
          <div className="text-4xl mb-4">⚠️</div>
          <p className="font-heading text-xl">{error || 'Fehler beim Laden der Daten'}</p>
          <Button 
            onClick={loadKPIData} 
            className="mt-4 bg-black text-white hover:bg-gray-800"
          >
            Erneut versuchen
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full bg-bg min-h-screen p-8">
      {/* Header mit korrektem Neobrutalism Styling */}
      <div className="flex items-center justify-between w-full mb-8">
        <div className="flex-1">
          <div className="flex items-center">
            <Home className="h-8 w-8 mr-2" />
            <h1 className="text-4xl font-heading tracking-tight text-text">{t("DASHBOARD")}</h1>
          </div>
          <p className="text-lg text-text font-base mt-2 opacity-70">{t("Shopfloormanagement")}</p>
        </div>
      </div>

      {/* Willkommens-Card */}
      <Card className="mb-8 border-2 border-[#ff7a05]">
        <CardContent className="p-2">
          <div className="flex justify-between items-start">
            <div>
              <h2 className="text-2xl font-heading text-black mb-2">Tagesübersicht für den ({getTargetDate()})</h2>
            </div>
            <div className="text-sm text-black font-base flex items-center gap-2 opacity-70">
              <Clock className="h-4 w-4" />
              Letzte Aktualisierung: {new Date().toLocaleTimeString('de-DE')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* KPI-Cards für Versand, Ablängerei und Wareneingang mit echten Daten */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        
        {/* Dispatch KPI-Card mit allen verfügbaren Daten */}
        <Card className="bg-blue-200 flex flex-col h-full border-2 border-[#ff7a05]">
          <CardHeader className="border-b-3 border-black">
            <CardTitle className="flex items-center justify-between text-black">
              <span>VERSAND</span>
              <Truck className="h-8 w-8" />
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4 flex-grow">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-base text-black">Servicegrad</span>
                <span className="font-heading text-lg text-black">{departmentKPIs.dispatch.serviceLevel.toFixed(1)}%</span>
              </div>
              <Progress value={departmentKPIs.dispatch.serviceLevel} className="h-3" />
              <div className="text-xs text-black font-base mt-1 opacity-70">
                Ziel: {departmentKPIs.dispatch.targetServiceLevel}%
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="text-sm font-base text-black">Picking-Leistung (ATRL+ARIL)</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.dispatch.pickingPerformance.toFixed(0)}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-base text-black">Lieferleistung</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.dispatch.deliveryPerformance.toFixed(1)}%</div>
              </div>
            </div>

            <div className="pt-2 border-t border-black/20 space-y-2">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black">Qualitätsrate</span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.qualityRate.toFixed(1)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black">Produzierte Tonnagen</span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.producedTonnage.toFixed(1)}t</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black">Direktverladung</span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.directLoading.toFixed(0)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black">Umschlag</span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.umschlag.toFixed(0)}</span>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex justify-between">
                  <span className="font-base text-black">kg/Colli</span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.kgPerColli.toFixed(1)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="font-base text-black">Elefanten</span>
                  <span className="font-heading text-black">{departmentKPIs.dispatch.elefanten.toFixed(0)}</span>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="bg-bg border-t-3 border-black p-4">
            <div className="flex justify-between w-full items-center">
              <div className="text-sm text-black font-base opacity-70">
                Aktualisiert {departmentKPIs.dispatch.lastUpdated}
              </div>
              <Button variant="sfm" size="sm">
                <Link to="/dispatch">Details</Link>
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Ablängerei KPI-Card mit allen verfügbaren Daten */}
        <Card className="bg-orange-200 flex flex-col h-full border-2 border-[#ff7a05]">
          <CardHeader className="border-b-3 border-black">
            <CardTitle className="flex items-center justify-between text-black">
              <span>ABLÄNGEREI</span>
              <Scissors className="h-8 w-8" />
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4 flex-grow">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-base text-black">Schnitte (Gesamt)</span>
                <span className="font-heading text-lg text-black">{departmentKPIs.ablaengerei.schnitte.toFixed(0)}</span>
              </div>
              <Progress value={Math.min((departmentKPIs.ablaengerei.schnitte / 1000) * 100, 100)} className="h-3" />
              <div className="text-xs text-black font-base mt-1 opacity-70">
                Schnittarten:
              </div>
            </div>
            
            <div className="grid grid-cols-4 gap-2">
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black">Schnitte TT</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteTT.toFixed(0)}</div>
              </div>
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black">Schnitte TR</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteTR.toFixed(0)}</div>
              </div>
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black">Schnitte RR</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteRR.toFixed(0)}</div>
              </div>
              <div className="space-y-1 text-center">
                <div className="text-sm font-base text-black">Cut2Pick</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.ablaengerei.schnitteCut2Pick.toFixed(0)}</div>
              </div>
            </div>

            {/* Platzhalter für einheitliche Höhe - unsichtbarer Spacer */}
            <div className="space-y-2">
              <div className="h-0"></div> {/* Spacer für gleiche Höhe */}
            </div>

            <div className="pt-2 border-t border-black/20">
              <div className="flex justify-between text-sm">
                <span className="font-base text-black">Qualitätsrate</span>
                <span className="font-heading text-black">{departmentKPIs.ablaengerei.qualityRate.toFixed(1)}%</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="bg-bg border-t-3 border-black p-4">
            <div className="flex justify-between w-full items-center">
              <div className="text-sm text-black font-base opacity-70">
                Aktualisiert {departmentKPIs.ablaengerei.lastUpdated}
              </div>
              <Button variant="sfm" size="sm" asChild>
                <Link to="/cutting">Details</Link>
              </Button>
            </div>
          </CardFooter>
        </Card>

        {/* Wareneingang KPI-Card mit allen verfügbaren Daten */}
        <Card className="bg-green-200 flex flex-col h-full border-2 border-[#ff7a05]">
          <CardHeader className="border-b-3 border-black">
            <CardTitle className="flex items-center justify-between text-black">
              <span>WARENEINGANG</span>
              <PackageCheck className="h-8 w-8" />
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 space-y-4 flex-grow">
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span className="font-base text-black">Gesamt WE-Positionen</span>
                <span className="font-heading text-lg text-black">{departmentKPIs.wareneingang.gesamtWE.toFixed(0)}</span>
              </div>
              <Progress value={Math.min((departmentKPIs.wareneingang.gesamtWE / 500) * 100, 100)} className="h-3" />
              <div className="text-xs text-black font-base mt-1 opacity-70">
                Lageraufteilung:
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1">
                <div className="text-sm font-base text-black">WE ATrL</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.wareneingang.weAtrlPos.toFixed(0)}</div>
              </div>
              <div className="space-y-1">
                <div className="text-sm font-base text-black">WE ManL</div>
                <div className="text-2xl font-heading text-black">{departmentKPIs.wareneingang.weManuellePos.toFixed(0)}</div>
              </div>
            </div>

            {/* Platzhalter für einheitliche Höhe - unsichtbarer Spacer */}
            <div className="space-y-2">
              <div className="h-0"></div> {/* Spacer für gleiche Höhe */}
            </div>

            <div className="pt-2 border-t border-black/20 space-y-2">
              <div className="flex justify-between text-sm">
                <span className="font-base text-black">Automatisierung vs. Manuell</span>
                <span className="font-heading text-black">{departmentKPIs.wareneingang.automatisierungsgrad.toFixed(1)}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="font-base text-black">Qualitätsrate</span>
                <span className="font-heading text-black">{departmentKPIs.wareneingang.qualityRate.toFixed(1)}%</span>
              </div>
            </div>
          </CardContent>
          <CardFooter className="bg-bg border-t-3 border-black p-4">
            <div className="flex justify-between w-full items-center">
              <div className="text-sm text-black font-base opacity-70">
                Aktualisiert {departmentKPIs.wareneingang.lastUpdated}
              </div>
              <Button variant="sfm" size="sm" asChild>
                <Link to="/incoming-goods">Details</Link>
              </Button>
            </div>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
}

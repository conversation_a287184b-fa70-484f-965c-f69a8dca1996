/**
 * Database Service für das Frontend
 * 
 * Ersetzt die direkten SQLite-Zugriffe durch HTTP-Requests an das Backend.
 * Bietet die gleiche API wie der alte database.ts für Rückwärtskompatibilität.
 */

import apiClient, { legacyDatabaseAPI } from './api.client';

/**
 * Hauptdatenbankservice für das Frontend
 * Delegiert alle Aufrufe an das Backend über HTTP-APIs
 */
class DatabaseService {
  // ===== CUTTING METHODS =====

  async getAblaengereiData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getAblaengereiData(startDate, endDate);
  }

  async getCuttingChartData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getCuttingChartData(startDate, endDate);
  }

  async getLagerCutsChartData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getLagerCutsChartData(startDate, endDate);
  }

  async getSchnitteData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getSchnitteData(startDate, endDate);
  }

  async getMaschinenH1ChartData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getMaschinenH1ChartData(startDate, endDate);
  }

  async getMaschinenH3ChartData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getMaschinenH3ChartData(startDate, endDate);
  }

  async getCuttingStats(startDate?: string, endDate?: string) {
    return apiClient.cutting.getCuttingStats(startDate, endDate);
  }

  // ===== WAREHOUSE METHODS =====

  async getAtrlData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getAtrlData(startDate, endDate);
  }

  async getAtrlChartData(startDate?: string, endDate?: string) {
    return apiClient.warehouse.getAtrlChartData(startDate, endDate);
  }

  async getArilData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getArilData(startDate, endDate);
  }

  async getArilChartData(startDate?: string, endDate?: string) {
    return apiClient.warehouse.getArilChartData(startDate, endDate);
  }

  async getWareneingangData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getWareneingangData(startDate, endDate);
  }

  async getServiceLevelData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getServiceLevelData(startDate, endDate);
  }

  async getServiceLevelChartData(startDate?: string, endDate?: string) {
    return apiClient.warehouse.getServiceLevelChartData(startDate, endDate);
  }

  async getPickingData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getPickingData(startDate, endDate);
  }

  async getPickingChartData(startDate?: string, endDate?: string) {
    return apiClient.warehouse.getPickingChartData(startDate, endDate);
  }

  async getDeliveryData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getDeliveryData(startDate, endDate);
  }

  async getDeliveryChartData(startDate?: string, endDate?: string) {
    return apiClient.warehouse.getDeliveryChartData(startDate, endDate);
  }

  async getWarehouseStats(startDate?: string, endDate?: string) {
    return apiClient.warehouse.getWarehouseStats(startDate, endDate);
  }

  // ===== SYSTEM METHODS =====

  async getSystemAtrlData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getSystemAtrlData(startDate, endDate);
  }

  async getSystemAtrlChartData(startDate?: string, endDate?: string) {
    return apiClient.system.getSystemAtrlChartData(startDate, endDate);
  }

  async getSystemArilData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getSystemArilData(startDate, endDate);
  }

  async getSystemArilChartData(startDate?: string, endDate?: string) {
    return apiClient.system.getSystemArilChartData(startDate, endDate);
  }

  async getQMData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getQMData(startDate, endDate);
  }

  async getQMChartData(startDate?: string, endDate?: string) {
    return apiClient.system.getQMChartData(startDate, endDate);
  }

  async getTagesleistungData(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getTagesleistungData(startDate, endDate);
  }

  async getTagesleistungChartData(startDate?: string, endDate?: string) {
    return apiClient.system.getTagesleistungChartData(startDate, endDate);
  }

  async getSystemStats(startDate?: string, endDate?: string) {
    return apiClient.system.getSystemStats(startDate, endDate);
  }

  // ===== COMBINED METHODS =====

  async getAllKPIData(startDate?: string, endDate?: string) {
    return apiClient.data.getAllKPIData(startDate, endDate);
  }

  async getAllChartData(startDate?: string, endDate?: string) {
    return apiClient.data.getAllChartData(startDate, endDate);
  }

  async getMaschinenEfficiency(startDate?: string, endDate?: string) {
    return legacyDatabaseAPI.getMaschinenEfficiency(startDate, endDate);
  }

  // ===== LEGACY COMPATIBILITY METHODS =====

  /**
   * Legacy-Methoden für Rückwärtskompatibilität
   * Diese Methoden werden von bestehenden Komponenten erwartet
   */

  // Datenbankverbindung (nicht mehr benötigt, aber für Kompatibilität)
  connectToDatabase(customPath?: string): void {
    console.log('Database connection is now handled by the backend');
  }

  // Tabellen-Informationen (vereinfacht)
  getDatabaseTables(): { name: string }[] {
    console.log('Table information is now handled by the backend');
    return [];
  }

  // Tabellendaten (vereinfacht)
  getTableData(tableName: string, limit: number = 100): { columns: string[]; rows: unknown[] } {
    console.log('Table data access is now handled by the backend');
    return { columns: [], rows: [] };
  }

  // Verbindung schließen (nicht mehr benötigt)
  close(): void {
    console.log('Database connection closing is now handled by the backend');
  }

  // Erstelle neue Instanz
  static create(): DatabaseService {
    return new DatabaseService();
  }
}

// Erstelle Singleton-Instanz
const databaseService = new DatabaseService();

// Exportiere die Service-Instanz als Standard-Export für Rückwärtskompatibilität
export default databaseService;

// Exportiere auch die Service-Klasse
export { DatabaseService };

// Exportiere die API-Clients für erweiterte Nutzung
export { apiClient, legacyDatabaseAPI };

// Re-exportiere Typen vom Backend (falls verfügbar)
export type {
  AblaengereiRecord,
  CuttingDataPoint,
  LagerCutsDataPoint,
  WEDataPoint,
  SystemAtrlDataPoint,
  SystemArilDataPoint,
  AtrlDataPoint,
  ArilDataPoint,
  SchnitteDataPoint,
  ServiceLevelDataPoint,
  PickingDataPoint,
  DeliveryDataPoint,
  QMDataPoint,
  TagesleistungDataPoint
} from '../types/database.types';

// ===== LEGACY IPC COMPATIBILITY =====

/**
 * Legacy-Funktionen für IPC-Kompatibilität
 * Diese werden von bestehenden Komponenten über window.electronAPI.database aufgerufen
 */
export const legacyIPCCompatibility = {
  // Cutting
  getAblaengereiData: () => legacyDatabaseAPI.getAblaengereiData(),
  getCuttingChartData: () => legacyDatabaseAPI.getCuttingChartData(),
  getLagerCutsChartData: () => legacyDatabaseAPI.getLagerCutsChartData(),
  getSchnitteData: () => legacyDatabaseAPI.getSchnitteData(),
  getMaschinenH1ChartData: () => legacyDatabaseAPI.getMaschinenH1ChartData(),
  getMaschinenH3ChartData: () => legacyDatabaseAPI.getMaschinenH3ChartData(),

  // Warehouse
  getAtrlData: () => legacyDatabaseAPI.getAtrlData(),
  getArilData: () => legacyDatabaseAPI.getArilData(),
  getWareneingangData: () => legacyDatabaseAPI.getWareneingangData(),
  getWEData: () => legacyDatabaseAPI.getWareneingangData(), // Alias
  getServiceLevelData: () => legacyDatabaseAPI.getServiceLevelData(),
  getPickingData: () => legacyDatabaseAPI.getPickingData(),
  getDeliveryData: () => legacyDatabaseAPI.getDeliveryData(),
  getDeliveryPositionsData: () => legacyDatabaseAPI.getDeliveryData(), // Alias

  // System
  getSystemAtrlData: () => legacyDatabaseAPI.getSystemAtrlData(),
  getSystemArilData: () => legacyDatabaseAPI.getSystemArilData(),
  getQMData: () => legacyDatabaseAPI.getQMData(),
  getReturnsData: () => legacyDatabaseAPI.getQMData(), // Alias
  getTagesleistungData: () => legacyDatabaseAPI.getTagesleistungData(),

  // Combined
  getMaschinenEfficiency: () => legacyDatabaseAPI.getMaschinenEfficiency()
};

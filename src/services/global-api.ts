/**
 * Globaler API-Client für das Frontend
 * 
 * Stellt die HTTP-API-Funktionen global zur Verfügung, damit sie von
 * bestehenden Komponenten ohne große Änderungen verwendet werden können.
 */

import apiClient from './api.client';

// Globale API-Funktionen für Rückwärtskompatibilität
const globalAPI = {
  // ===== CUTTING APIS =====
  getAblaengereiData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.cutting.getAblaengereiData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Ablängerei-Daten:', error);
      return [];
    }
  },

  getCuttingChartData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.cutting.getCuttingChartData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Cutting-Chart-Daten:', error);
      return [];
    }
  },

  getLagerCutsChartData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.cutting.getLagerCutsChartData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Lager-Cuts-Chart-Daten:', error);
      return [];
    }
  },

  getSchnitteData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.cutting.getSchnitteData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Schnittdaten:', error);
      return [];
    }
  },

  getMaschinenH1ChartData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.cutting.getMaschinenH1ChartData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Maschinen-H1-Daten:', error);
      return [];
    }
  },

  getMaschinenH3ChartData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.cutting.getMaschinenH3ChartData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Maschinen-H3-Daten:', error);
      return [];
    }
  },

  // ===== WAREHOUSE APIS =====
  getAtrlData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.warehouse.getAtrlData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der ATrL-Daten:', error);
      return [];
    }
  },

  getArilData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.warehouse.getArilData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der ARiL-Daten:', error);
      return [];
    }
  },

  getWareneingangData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.warehouse.getWareneingangData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Wareneingang-Daten:', error);
      return [];
    }
  },

  getWEData: async (startDate?: string, endDate?: string) => {
    // Alias für getWareneingangData
    return globalAPI.getWareneingangData(startDate, endDate);
  },

  getServiceLevelData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.warehouse.getServiceLevelData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Service-Level-Daten:', error);
      return [];
    }
  },

  getPickingData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.warehouse.getPickingData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Picking-Daten:', error);
      return [];
    }
  },

  getDeliveryData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.warehouse.getDeliveryData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Lieferungs-Daten:', error);
      return [];
    }
  },

  getDeliveryPositionsData: async (startDate?: string, endDate?: string) => {
    // Alias für getDeliveryData
    return globalAPI.getDeliveryData(startDate, endDate);
  },

  // ===== SYSTEM APIS =====
  getSystemAtrlData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.system.getSystemAtrlData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der System-ATrL-Daten:', error);
      return [];
    }
  },

  getSystemArilData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.system.getSystemArilData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der System-ARiL-Daten:', error);
      return [];
    }
  },

  getQMData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.system.getQMData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der QM-Daten:', error);
      return [];
    }
  },

  getReturnsData: async (startDate?: string, endDate?: string) => {
    // Alias für getQMData
    return globalAPI.getQMData(startDate, endDate);
  },

  getTagesleistungData: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.system.getTagesleistungData(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Tagesleistungs-Daten:', error);
      return [];
    }
  },

  // ===== COMBINED APIS =====
  getMaschinenEfficiency: async (startDate?: string, endDate?: string) => {
    try {
      return await apiClient.data.getMaschinenEfficiency(startDate, endDate);
    } catch (error) {
      console.error('Fehler beim Abrufen der Maschinen-Effizienz-Daten:', error);
      return [];
    }
  },

  // ===== LEGACY COMPATIBILITY =====
  getCuttingsData: async (startDate?: string, endDate?: string) => {
    // Alias für getCuttingChartData
    return globalAPI.getCuttingChartData(startDate, endDate);
  },

  getLagerCutsData: async (startDate?: string, endDate?: string) => {
    // Alias für getLagerCutsChartData
    return globalAPI.getLagerCutsChartData(startDate, endDate);
  }
};

// Stelle die API global zur Verfügung
if (typeof window !== 'undefined') {
  // Im Browser/Renderer-Prozess
  (window as any).globalDatabaseAPI = globalAPI;
  
  // Überschreibe auch die electronAPI.database für Kompatibilität
  if ((window as any).electronAPI) {
    (window as any).electronAPI.database = {
      ...(window as any).electronAPI.database,
      ...globalAPI
    };
  } else {
    (window as any).electronAPI = {
      database: globalAPI,
      invoke: () => Promise.resolve()
    };
  }
}

export default globalAPI;

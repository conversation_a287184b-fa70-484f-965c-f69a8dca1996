import type { Config } from 'tailwindcss';

export default {
  darkMode: 'class',
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      // Neobrutalism Farben basierend auf der offiziellen Dokumentation
      colors: {
        // Offizielle Neobrutalism Farben
        main: '#88aaee',
        mainAccent: '#4d80e6',
        overlay: 'rgba(0,0,0,0.8)',

        // Light mode
        bg: '#dfe5f2',
        text: '#000',
        border: '#000',

        // Dark mode  
        darkBg: '#272933',
        darkText: '#eeefe9',
        darkBorder: '#000',
        secondaryBlack: '#212121',
        
        // Zusätzliche Neobrutalism Farben für KPIs
        'neo-red': '#FF5470',
        'neo-blue': '#2BD9FE', 
        'neo-yellow': '#FFB800',
        'neo-green': '#00C6CF',
        'neo-pink': '#FF90E8',
        'neo-purple': '#7B61FF',
        
        // SFM-spezifische Farben
        'sfm-orange': '#ff7a05',
        
        // Shadcn/ui Farben für Kompatibilität
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      
      // Neobrutalism border radius (5px base)
      borderRadius: {
        base: '5px',
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)", 
        sm: "calc(var(--radius) - 4px)",
      },
      
      // Neobrutalism box shadows (4px 4px standard)
      boxShadow: {
        light: '4px 4px 0px 0px #000',
        dark: '4px 4px 0px 0px #000',
        'brutal-sm': '2px 2px 0px 0px #000',
        'brutal': '4px 4px 0px 0px #000', 
        'brutal-lg': '6px 6px 0px 0px #000',
        'brutal-xl': '8px 8px 0px 0px #000',
      },
      
      // Neobrutalism translate values
      translate: {
        boxShadowX: '4px',
        boxShadowY: '4px', 
        reverseBoxShadowX: '-4px',
        reverseBoxShadowY: '-4px',
      },
      
      // Neobrutalism font weights
      fontWeight: {
        base: '500',
        heading: '700',
      },
      
      // Custom border widths
      borderWidth: {
        '3': '3px',
        '4': '4px', 
        '5': '5px',
        '6': '6px',
      },
      
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [],
} satisfies Config;

# build-portable.ps1
# Automatisches Build-Skript für SFM Electron Dashboard
# Erstellt eine portable EXE-Datei mit allen notwendigen Dateien

Write-Host "🚀 Starte Portable Build für SFM Dashboard..." -ForegroundColor Green

# 1. Cleanup alter Builds
Write-Host "🧹 Cleanup alter Builds..." -ForegroundColor Yellow
taskkill /F /IM LappDashboard.exe 2>$null
taskkill /F /IM electron.exe 2>$null
Remove-Item -Recurse -Force portable-build -ErrorAction SilentlyContinue
Remove-Item -Recurse -Force out -ErrorAction SilentlyContinue

# Kurz warten bis Prozesse beendet sind
Start-Sleep -Seconds 2

# 2. Build erstellen
Write-Host "🔨 Erstelle portable Build..." -ForegroundColor Yellow
npm run build:simple-portable
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Build fehlgeschlagen!" -ForegroundColor Red
    Write-Host "Mögliche Ursachen:" -ForegroundColor Yellow
    Write-Host "- Node.js nicht installiert" -ForegroundColor Yellow
    Write-Host "- Dependencies fehlen (npm install)" -ForegroundColor Yellow
    Write-Host "- Prozesse laufen noch im Hintergrund" -ForegroundColor Yellow
    exit 1
}

# 3. Überprüfe ob Build-Ordner existiert
$buildPath = "portable-build\LappDashboard-win32-x64"
if (-not (Test-Path $buildPath)) {
    Write-Host "❌ Build-Ordner nicht gefunden: $buildPath" -ForegroundColor Red
    exit 1
}

# 4. Datenbank kopieren
Write-Host "💾 Kopiere Datenbank..." -ForegroundColor Yellow
$dbSourcePath = "database\sfm_dashboard.db"
$dbTargetDir = "$buildPath\database"
$dbTargetPath = "$dbTargetDir\sfm_dashboard.db"

if (-not (Test-Path $dbSourcePath)) {
    Write-Host "⚠️  Warnung: Datenbank nicht gefunden: $dbSourcePath" -ForegroundColor Yellow
    Write-Host "App wird ohne Datenbank erstellt." -ForegroundColor Yellow
} else {
    New-Item -ItemType Directory -Path $dbTargetDir -Force | Out-Null
    Copy-Item $dbSourcePath $dbTargetPath -Force
    $dbSize = (Get-Item $dbTargetPath).Length / 1MB
    Write-Host "✅ Datenbank kopiert ($([math]::Round($dbSize, 2)) MB)" -ForegroundColor Green
}

# 5. Vite-Dateien kopieren
Write-Host "📁 Kopiere Vite-Dateien..." -ForegroundColor Yellow
$viteSourcePath = ".vite"
$viteTargetPath = "$buildPath\resources\app\.vite"

if (-not (Test-Path $viteSourcePath)) {
    Write-Host "❌ Vite-Dateien nicht gefunden: $viteSourcePath" -ForegroundColor Red
    Write-Host "Führe zuerst 'npm run build:forge' aus." -ForegroundColor Yellow
    exit 1
}

Copy-Item "$viteSourcePath\*" $viteTargetPath -Recurse -Force

# 6. Validierung der wichtigen Dateien
Write-Host "🔍 Validiere Build-Dateien..." -ForegroundColor Yellow

$validationChecks = @(
    @{ Path = "$buildPath\LappDashboard.exe"; Name = "Hauptprogramm" },
    @{ Path = "$buildPath\resources\app\.vite\build\main.js"; Name = "Backend (main.js)" },
    @{ Path = "$buildPath\resources\app\.vite\build\preload.js"; Name = "Preload-Skript" },
    @{ Path = "$buildPath\resources\app\.vite\renderer\main_window\index.html"; Name = "Frontend (HTML)" },
    @{ Path = "$buildPath\resources\app\.vite\renderer\main_window\assets"; Name = "Frontend Assets" }
)

$allValid = $true
foreach ($check in $validationChecks) {
    if (Test-Path $check.Path) {
        Write-Host "✅ $($check.Name)" -ForegroundColor Green
    } else {
        Write-Host "❌ $($check.Name) fehlt: $($check.Path)" -ForegroundColor Red
        $allValid = $false
    }
}

# Optional: Images-Ordner kopieren falls vorhanden
$imagesSource = "images"
$imagesTarget = "$buildPath\resources\app\images"
if (Test-Path $imagesSource) {
    Copy-Item $imagesSource $imagesTarget -Recurse -Force
    Write-Host "✅ Images-Ordner kopiert" -ForegroundColor Green
}

# 7. Finale Validierung und Erfolgsmeldung
if ($allValid) {
    Write-Host ""
    Write-Host "🎉 Build erfolgreich erstellt!" -ForegroundColor Green
    Write-Host "📍 Pfad: $buildPath\LappDashboard.exe" -ForegroundColor Cyan
    
    # Dateigröße anzeigen
    $exeSize = (Get-Item "$buildPath\LappDashboard.exe").Length / 1MB
    Write-Host "📊 EXE-Größe: $([math]::Round($exeSize, 2)) MB" -ForegroundColor Cyan
    
    if (Test-Path $dbTargetPath) {
        $totalSize = (Get-ChildItem $buildPath -Recurse | Measure-Object -Property Length -Sum).Sum / 1MB
        Write-Host "📊 Gesamt-Größe: $([math]::Round($totalSize, 2)) MB" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "🔧 Nächste Schritte:" -ForegroundColor Yellow
    Write-Host "1. App testen: cd '$buildPath' && .\LappDashboard.exe" -ForegroundColor White
    Write-Host "2. Auf anderem PC testen (ohne Development-Tools)" -ForegroundColor White
    Write-Host "3. Bei Problemen: BUILD-ANLEITUNG.md lesen" -ForegroundColor White
} else {
    Write-Host ""
    Write-Host "❌ Build unvollständig - einige wichtige Dateien fehlen!" -ForegroundColor Red
    Write-Host "Überprüfe die Fehlermeldungen oben und versuche es erneut." -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "Build-Prozess abgeschlossen. ✨" -ForegroundColor Green 
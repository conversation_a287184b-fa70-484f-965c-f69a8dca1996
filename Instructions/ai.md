# KI-Integration Brainstorming für SFM-Electron

## Notes
- Erste KI-Vorschläge waren zu allgemein und nicht spezifisch auf die App zugeschnitten.
- Die App enthält Seiten wie IncomingGoodsPage, DispatchPage, CuttingPage, ArilPage, AtrlPage – vermutlich Lager/Produktion.
- Es wurde mit der gezielten Analyse der Seiten IncomingGoodsPage und CuttingPage begonnen.
- Die App ist ein Leitstand für Meterware (Kabel auf Trommeln), arbeitet abteilungsübergreifend mit Zahlen & Diagrammen und ist an eine Datenbank angebunden.
- Schnittplanung und Restlängenoptimierung sind zentrale Herausforderungen (maximale Nutzung der Trommellängen).
- Es werden ausschließlich Zahlen/Daten aus der Intralogistik verarbeitet (keine Bilderkennung, keine Vor-Ort-Bilderfassung). Fokus: Lagerbewegungen, Bestände, Ein-/Ausgänge, Umlagerungen, Kommissionierung, Rücklagerungen, Verkäufe, ABC-Bestände.
- Ziel: Umsetzung eines klassischen AI-Chatbots (Bubble unten rechts, öffnet Chatbereich) mit OpenRouter-API für flexible Modellauswahl.
- Nächster Schritt: Umsetzung eines klassischen AI-Chatbots (Bubble unten rechts, öffnet Chatbereich) mit OpenRouter-API für flexible Modellauswahl.
- OpenRouter Presets übernehmen Systemprompt und LLM-Konfiguration, kein Prompt im Code nötig (siehe PresetsOpenRouter.md)
- Chat-Bubble ist im aktuellen UI nicht sichtbar, Integration ist noch nicht abgeschlossen.
- Presets sind konfiguriert, aber die Bubble-Sichtbarkeit und -Funktionalität müssen noch getestet werden.
- Health-Endpoint im Backend implementiert, Server läuft wieder.
- Backend-Start schlägt fehl: OPENAI_API_KEY Umgebungsvariable fehlt oder ist leer (siehe Terminal-Fehler), Blocker für die Chatbot-Funktion.
- dotenv-Konfiguration und Debug-Ausgabe in server.ts ergänzt, um Umgebungsvariablen sicher aus Hauptverzeichnis zu laden.
- Falscher Pfad zur .env-Datei (liegt in SFM-Electron, nicht in Lapp-Hauptverzeichnis) verhindert Laden der Variablen und Backend-Start.

## Task List
- [x] Erste allgemeine KI-Ideen gesammelt
- [x] Projektstruktur und Seiten gezielt analysieren
- [x] App-spezifische KI-Anwendungsfälle brainstormen
- [x] Passenden LLM-Provider (API/Cloud/Open Source lokal) recherchieren und bewerten
- [x] Mit Nutzer Präferenz für LLM-Provider abstimmen
- [x] Technische Integration OpenRouter-API vorbereiten
- [x] API-Backend für Chatbot-Anbindung (OpenRouter) implementieren
- [x] Chat-Komponente (Bubble + Chatbereich) erstellen und einbinden
- [ ] Chat-Bubble sichtbar machen und Funktion testen
- [ ] AI-Chatbot (Bubble + Chatbereich) in App integrieren
- [ ] Mit Nutzer abstimmen, welche Ideen weiter ausgearbeitet werden sollen

## Current Goal
Chat-Bubble sichtbar machen und Funktion testen

## Brainstorming Ergebnisse

1. Restlängen-Optimierung (CuttingPage)
- KI-gestützte Berechnung optimaler Schnittpläne für Meterware-Aufträge (maximale Trommelausnutzung, minimale Reste)
- Automatische Vorschläge zur Auftragsreihenfolge für weniger Verschnitt

2. Optimierung der Zuschnittsplanung (CuttingPage)
- KI-Algorithmen zur Materialausnutzungsoptimierung
- Vorhersage des Materialbedarfs basierend auf historischen Daten

3. Intelligente Kennzahlen-Analyse (Dashboard)
- KI erkennt Auffälligkeiten und Trends in Produktions-/Lagerdaten und warnt automatisch
- Automatische Generierung von Handlungsempfehlungen bei Abweichungen

4. Lageroptimierung (IncomingGoodsPage)
- Intelligente Lagerplatzvergabe basierend auf Zugriffshäufigkeit
- Vorhersage von Engpässen

5. Automatisierte Dokumentenverarbeitung
- KI-gestützte Extraktion von Daten aus Lieferscheinen/Rechnungen

---

## Finde ich gut

1. Optimale Zuschnittsplanung (CuttingPage)
- Bin-Packing Algorithmen für maximale Trommelausnutzung
- Berücksichtigung von:
    - Verschiedenen Kabeltypen und -längen
    - Verfügbaren Restlängen
    - Auftragsprioritäten
    - Automatische Vorschläge für optimale Schnittmuster

2. Intelligente Bestandsführung
- ABC-Analyse 2.0 mit dynamischer Klassifizierung
- Automatische Anpassung der Bestellmengen und -zeitpunkte
- Vorhersage von Bestandsengpässen

3. Lageroptimierung
- Dynamische Lagerplatzvergabe basierend auf:
    - Zugriffshäufigkeit
    - Artikelverwandtschaft (häufig zusammen benötigte Artikel)
    - Gewicht und Größe
- Optimale Kommissionierwege

4. Bewegungsdaten-Analyse
- Anomalie-Erkennung bei Lagerbewegungen
- Automatische Erkennung von:
    - Ungewöhnlichen Bewegungsmustern
    - Häufigen Umlagerungen
    - Ineffizienten Prozessabläufen

5. Prozessoptimierung
- Simulation von Prozessänderungen
- Identifikation von Engpässen
- Automatisierte Vorschläge für Prozessverbesserungen

6. Echtzeit-Kennzahlenanalyse
- Predictive Analytics für:
    - Erwartete Lagerbestände
    - Auslastungsprognosen
    - Kapazitätsplanung
- Automatische Warnungen bei kritischen Werten

7. Ressourcenoptimierung
- Maschinenauslastung vorhersagen
- Optimale Auftragsreihenfolge
- Energieverbrauchsanalyse

8. Lieferketten-Optimierung
- Vorhersage von Lieferzeiten
- Risikoanalyse bei Lieferverzögerungen
- Alternative Lieferantenbewertung

9. Berichtswesen
- Automatisierte Berichtserstellung
- Zusammenfassung wichtiger KPIs
- Handlungsempfehlungen

# 10. AI-Chatbot auf der App
- [ ] Beantwortet Fragen zu App
- [ ] Beantwortet Fragen zu Datenbank und Kennzahlen
- [ ] Gibt automatisch Vorschläge
- [ ] Hat seinen Systemprompt
- [ ] Hat seine RAG Daten
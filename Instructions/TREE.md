SFM Electron\
├── database
│   ├── imigration
│   │   ├── ergcsv.csv
│   │   ├── zusammen.xlsx
│   │   ├── zusammencsv.csv
│   │   └── zusammencsv_clean.csv
│   ├── check-data.js
│   ├── check-schema.js
│   ├── clear-data.js
│   ├── import-csv.js
│   └── init-db.js
├── images
├── Instructions
│   └── INSTRUCTIONS.md
├── src
│   ├── assets
│   │   └── fonts
│   │       ├── geist
│   │       │   └── geist.ttf
│   │       ├── geist-mono
│   │       │   └── geist-mono.ttf
│   │       └── tomorrow
│   │           ├── tomorrow-bold-italic.ttf
│   │           ├── tomorrow-bold.ttf
│   │           ├── tomorrow-italic.ttf
│   │           └── tomorrow-regular.ttf
│   ├── components
│   │   ├── charts
│   │   │   ├── CuttingsChart.tsx
│   │   │   ├── DailyPerformanceChart.tsx
│   │   │   ├── DeliveryPositionsChart.tsx
│   │   │   ├── IncomingPositionsChart.tsx
│   │   │   ├── PickingChart.tsx
│   │   │   ├── ReturnsChart.tsx
│   │   │   └── ServiceLevelChart.tsx
│   │   ├── stats
│   │   │   ├── CorrelationStats.tsx
│   │   │   ├── InventoryStats.tsx
│   │   │   ├── KPIDashboard.tsx
│   │   │   ├── LogisticsStats.tsx
│   │   │   ├── ProductivityStats.tsx
│   │   │   └── QualityStats.tsx
│   │   ├── template
│   │   │   ├── Footer.tsx
│   │   │   ├── InitialIcons.tsx
│   │   │   └── NavigationMenu.tsx
│   │   ├── ui
│   │   │   ├── badge.tsx
│   │   │   ├── button.tsx
│   │   │   ├── card.tsx
│   │   │   ├── chart.tsx
│   │   │   ├── dialog.tsx
│   │   │   ├── expandable-chart.tsx
│   │   │   ├── navigation-menu.tsx
│   │   │   ├── progress.tsx
│   │   │   ├── stat-card.tsx
│   │   │   ├── toast.tsx
│   │   │   ├── toaster.tsx
│   │   │   ├── toggle-group.tsx
│   │   │   ├── toggle.tsx
│   │   │   └── use-toast.ts
│   │   ├── areaChart-stacked.tsx
│   │   ├── DragWindowRegion.tsx
│   │   ├── LangToggle.tsx
│   │   ├── Sidebar.tsx
│   │   ├── stagewise-toolbar.tsx
│   │   ├── theme-provider.tsx
│   │   └── ToggleTheme.tsx
│   ├── helpers
│   │   ├── ipc
│   │   │   ├── database
│   │   │   │   └── database-context.ts
│   │   │   ├── theme
│   │   │   │   ├── theme-channels.ts
│   │   │   │   ├── theme-context.ts
│   │   │   │   └── theme-listeners.ts
│   │   │   ├── window
│   │   │   │   ├── window-channels.ts
│   │   │   │   ├── window-context.ts
│   │   │   │   └── window-listeners.ts
│   │   │   ├── context-exposer.ts
│   │   │   ├── database-handlers.ts
│   │   │   └── listeners-register.ts
│   │   ├── language_helpers.ts
│   │   ├── theme_helpers.ts
│   │   └── window_helpers.ts
│   ├── i18n
│   │   └── de.json
│   ├── layouts
│   │   ├── BaseLayout.tsx
│   │   └── DashboardLayout.tsx
│   ├── lib
│   │   ├── db.ts
│   │   └── utils.ts
│   ├── localization
│   │   ├── i18n.ts
│   │   ├── langs.ts
│   │   └── language.ts
│   ├── pages
│   │   ├── CuttingPage.tsx
│   │   ├── DispatchPage.tsx
│   │   ├── HomePage.tsx
│   │   ├── IncomingGoodsPage.tsx
│   │   ├── SecondPage.tsx
│   │   └── StatsPage.tsx
│   ├── routes
│   │   ├── __root.tsx
│   │   ├── router.tsx
│   │   └── routes.tsx
│   ├── services
│   │   ├── csvImporter.ts
│   │   └── database.ts
│   ├── styles
│   │   ├── global.css
│   │   └── neobrutalism.css
│   ├── tests
│   │   ├── e2e
│   │   │   └── example.test.ts
│   │   └── unit
│   │       ├── setup.ts
│   │       └── sum.test.ts
│   ├── types
│   │   └── database.d.ts
│   ├── utils
│   │   └── tailwind.ts
│   ├── App.tsx
│   ├── main.ts
│   ├── preload.ts
│   ├── renderer.tsx
│   ├── SimpleApp.tsx
│   ├── TestApp.tsx
│   ├── types.d.ts
│   └── vite-env.d.ts
├── temp
├── .gitattributes
├── .gitignore
├── .prettierignore
├── .prettierrc
├── components.json
├── eslint.config.mjs
├── forge.config.ts
├── forge.env.d.ts
├── index.html
├── LICENSE
├── package-lock.json
├── package.json
├── playwright.config.ts
├── postcss.config.js
├── README.md
├── STAGEWISE_INTEGRATION.md
├── tailwind.config.ts
├── tsconfig.json
├── vite.config.ts
├── vite.main.config.ts
├── vite.preload.config.ts
├── vite.renderer.config.mts
└── vitest.config.ts

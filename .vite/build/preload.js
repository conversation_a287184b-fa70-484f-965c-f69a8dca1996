"use strict";
const electron = require("electron");
const THEME_MODE_CURRENT_CHANNEL = "theme-mode:current";
const THEME_MODE_TOGGLE_CHANNEL = "theme-mode:toggle";
const THEME_MODE_DARK_CHANNEL = "theme-mode:dark";
const THEME_MODE_LIGHT_CHANNEL = "theme-mode:light";
const THEME_MODE_SYSTEM_CHANNEL = "theme-mode:system";
function exposeThemeContext() {
  const { contextBridge, ipcRenderer } = window.require("electron");
  contextBridge.exposeInMainWorld("themeMode", {
    current: () => ipcRenderer.invoke(THEME_MODE_CURRENT_CHANNEL),
    toggle: () => ipcRenderer.invoke(THEME_MODE_TOGGLE_CHANNEL),
    dark: () => ipcRenderer.invoke(THEME_MODE_DARK_CHANNEL),
    light: () => ipcRenderer.invoke(THEME_MODE_LIGHT_CHANNEL),
    system: () => ipcRenderer.invoke(THEME_MODE_SYSTEM_CHANNEL)
  });
}
const WIN_MINIMIZE_CHANNEL = "window:minimize";
const WIN_MAXIMIZE_CHANNEL = "window:maximize";
const WIN_CLOSE_CHANNEL = "window:close";
function exposeWindowContext() {
  const { contextBridge, ipcRenderer } = window.require("electron");
  contextBridge.exposeInMainWorld("electronWindow", {
    minimize: () => ipcRenderer.invoke(WIN_MINIMIZE_CHANNEL),
    maximize: () => ipcRenderer.invoke(WIN_MAXIMIZE_CHANNEL),
    close: () => ipcRenderer.invoke(WIN_CLOSE_CHANNEL)
  });
}
function exposeDatabaseContext() {
  const databaseAPI = {
    /**
     * Ruft die Servicelevel-Daten für das Diagramm ab
     * @returns Promise mit den Servicelevel-Daten
     */
    getServiceLevelData: () => electron.ipcRenderer.invoke("get-service-level-data"),
    /**
     * Ruft die Kommissionierungsdaten für das Diagramm ab
     * @returns Promise mit den Kommissionierungsdaten
     */
    getPickingData: () => electron.ipcRenderer.invoke("get-picking-data"),
    /**
     * Ruft die Retourendaten für das Diagramm ab
     * @returns Promise mit den Retourendaten
     */
    getReturnsData: () => electron.ipcRenderer.invoke("get-returns-data"),
    /**
     * Ruft die Lieferpositionsdaten für das Diagramm ab
     * @returns Promise mit den Lieferpositionsdaten
     */
    getDeliveryPositionsData: () => electron.ipcRenderer.invoke("get-delivery-positions-data"),
    /**
     * Ruft die Tagesleistungsdaten für das Diagramm ab
     * @returns Promise mit den Tagesleistungsdaten
     */
    getTagesleistungData: () => electron.ipcRenderer.invoke("get-tagesleistung-data"),
    /**
     * Ruft die Daten aus der Ablaengerei-Tabelle ab
     * @returns Promise mit den Ablaengerei-Daten
     */
    getAblaengereiData: () => electron.ipcRenderer.invoke("get-ablaengerei-data"),
    /**
     * Ruft die Lagerdaten für den LagerCutsChart ab
     * @returns Promise mit den Lagerdaten
     */
    getLagerCutsData: () => electron.ipcRenderer.invoke("get-lager-cuts-data"),
    /**
     * Ruft die Schneidedaten für den CuttingsChart ab
     * @returns Promise mit den Schneidedaten
     */
    getCuttingsData: () => electron.ipcRenderer.invoke("get-cuttings-data"),
    /**
     * Ruft die Chart-formattierten Lagerdaten ab (neue Service-Methode)
     * @returns Promise mit den gefilterten Lagerdaten
     */
    getLagerCutsChartData: () => electron.ipcRenderer.invoke("get-lager-cuts-chart-data"),
    /**
     * Ruft die Chart-formattierten Schneidedaten ab (neue Service-Methode)
     * @returns Promise mit den gefilterten Schneidedaten
     */
    getCuttingChartData: () => electron.ipcRenderer.invoke("get-cutting-chart-data"),
    /**
     * Ruft die WE-Daten (Wareneingang) für das Diagramm ab
     * @returns Promise mit den WE-Daten
     */
    getWEData: () => electron.ipcRenderer.invoke("get-we-data"),
    /**
     * Ruft die System-ATRL-Daten für das Diagramm ab
     * @returns Promise mit den System-ATRL-Daten
     */
    getSystemAtrlData: () => electron.ipcRenderer.invoke("get-system-atrl-data"),
    /**
     * Ruft die System-ARIL-Daten für das Diagramm ab
     * @returns Promise mit den System-ARIL-Daten
     */
    getSystemArilData: () => electron.ipcRenderer.invoke("get-system-aril-data"),
    /**
     * Ruft die ATrL-Daten für das Diagramm ab
     * @returns Promise mit den ATrL-Daten
     */
    getAtrlData: () => electron.ipcRenderer.invoke("get-atrl-data"),
    /**
     * Ruft die ARiL-Daten für das Diagramm ab
     * @returns Promise mit den ARiL-Daten
     */
    getArilData: () => electron.ipcRenderer.invoke("get-aril-data"),
    /**
     * Ruft die Schnittedaten aus der schnitte-Tabelle ab
     * @returns Promise mit den Schnittedaten
     */
    getSchnitteData: () => electron.ipcRenderer.invoke("get-schnitte-data"),
    /**
     * Ruft die Maschinen-Effizienz-Daten ab (Ist vs. Soll Schnitte pro Stunde)
     * @returns Promise mit den Effizienz-Daten
     */
    getMaschinenEfficiency: () => electron.ipcRenderer.invoke("get-maschinen-efficiency")
  };
  const databaseManagerAPI = {
    /**
     * Stellt eine Verbindung zur Datenbank her
     * @param customPath Optionaler benutzerdefinierter Pfad zur Datenbank
     * @returns Promise mit dem Verbindungsstatus
     */
    connectToDatabase: (customPath) => electron.ipcRenderer.invoke("database:connect", customPath),
    /**
     * Ruft alle Tabellen der Datenbank ab
     * @returns Promise mit der Liste der Tabellen
     */
    getDatabaseTables: () => electron.ipcRenderer.invoke("database:get-tables"),
    /**
     * Ruft die Daten einer bestimmten Tabelle ab
     * @param tableName Name der Tabelle
     * @param limit Maximale Anzahl der zurückzugebenden Zeilen
     * @returns Promise mit den Tabellendaten
     */
    getTableData: (tableName, limit) => electron.ipcRenderer.invoke("database:get-table-data", tableName, limit)
  };
  electron.contextBridge.exposeInMainWorld("electronAPI", {
    database: databaseAPI,
    // Neue Datenbankmanager-Funktionen
    ...databaseManagerAPI,
    // Generische invoke-Funktion für alle IPC-Aufrufe
    invoke: (channel, ...args) => electron.ipcRenderer.invoke(channel, ...args)
  });
  console.log("Datenbank-API und invoke-Funktion wurden im Renderer-Prozess verfügbar gemacht.");
}
function exposeContexts() {
  exposeWindowContext();
  exposeThemeContext();
  exposeDatabaseContext();
  console.log("Alle Kontexte wurden im Renderer-Prozess verfügbar gemacht.");
}
exposeContexts();
